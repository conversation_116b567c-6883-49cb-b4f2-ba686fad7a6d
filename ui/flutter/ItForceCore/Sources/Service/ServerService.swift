/**
 * FILE: ServerService.swift
 *
 * DESCRIPTION:
 *     Application layer server service that provides high-level server management functionality.
 *     Encapsulates ServerManager from Domain layer and provides simplified business interfaces
 *     for Platform Channel and UI components. Handles server configuration, state management,
 *     and event notification forwarding.
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create
 */

import Foundation
import OSLog

/**
 * NAME: ServerServiceConfiguration
 *
 * DESCRIPTION:
 *     Configuration structure for server service.
 *     Contains service-level settings and preferences.
 *
 * PROPERTIES:
 *     autoSelectBestServer - Whether to automatically select best server
 *     enableBackgroundPing - Whether to enable background ping monitoring
 *     serverListCacheTimeout - Server list cache timeout in seconds
 *     maxRetryAttempts - Maximum retry attempts for operations
 *     retryDelay - Delay between retry attempts
 */
public struct ServerServiceConfiguration: BaseServiceConfiguration {
    // MARK: - BaseServiceConfiguration Implementation
    public let retry: RetryConfiguration
    public let timeout: TimeoutConfiguration
    public let monitoring: MonitoringConfiguration

    // MARK: - Server-Specific Configuration
    public let autoSelectBestServer: Bool
    public let serverListCacheTimeout: TimeInterval
    public let pingInterval: TimeInterval

    public init(
        retry: RetryConfiguration = .default,
        timeout: TimeoutConfiguration = .default,
        monitoring: MonitoringConfiguration = .default,
        autoSelectBestServer: Bool = true,
        serverListCacheTimeout: TimeInterval = 3600, // 1 hour to match Android/Go backend
        pingInterval: TimeInterval = 30.0 // 30 seconds ping interval
    ) {
        self.retry = retry
        self.timeout = timeout
        self.monitoring = monitoring
        self.autoSelectBestServer = autoSelectBestServer
        self.serverListCacheTimeout = serverListCacheTimeout
        self.pingInterval = pingInterval
    }

    public static let `default` = ServerServiceConfiguration()
}

/**
 * NAME: ServerServiceEvent
 *
 * DESCRIPTION:
 *     Server service event types for notification system.
 *     Used to notify upper layers about server-related changes.
 */
public enum ServerServiceEvent: String, CaseIterable, Sendable {
    case serverListUpdated = "server_list_updated"
    case serverSelected = "server_selected"
    case serverStatusChanged = "server_status_changed"
    case pingCompleted = "ping_completed"
    case bestServerChanged = "best_server_changed"
    case networkChanged = "network_changed"
}

/**
 * NAME: ServerServiceError
 *
 * DESCRIPTION:
 *     Server service specific errors with user-friendly messages.
 *     Provides business-level error information for UI display.
 */
public enum ServerServiceError: Error, LocalizedError, Sendable {
    case serviceNotStarted
    case serverManagerError(ServerManagerError)
    case configurationInvalid(String)
    case operationTimeout
    case noServersConfigured
    case serverSelectionFailed(String)
    
    public var errorDescription: String? {
        switch self {
        case .serviceNotStarted:
            return "Server service is not started. Please start the service first."
        case .serverManagerError(let error):
            return "Server management error: \(error.localizedDescription)"
        case .configurationInvalid(let reason):
            return "Invalid server configuration: \(reason)"
        case .operationTimeout:
            return "Server operation timed out. Please check your network connection."
        case .noServersConfigured:
            return "No servers are configured. Please add server configuration."
        case .serverSelectionFailed(let reason):
            return "Failed to select server: \(reason)"
        }
    }
}

/**
 * NAME: ServerServiceDelegate
 *
 * DESCRIPTION:
 *     Delegate protocol for server service events.
 *     Allows upper layers to receive server-related notifications.
 */
public protocol ServerServiceDelegate: AnyObject, Sendable {
    func serverService(_ service: ServerService, didUpdateServerList servers: [ServerInfo])
    func serverService(_ service: ServerService, didSelectServer server: ServerInfo?)
    func serverService(_ service: ServerService, didChangeServerStatus serverID: String, status: ServerStatus)
    func serverService(_ service: ServerService, didCompletePing results: [String: PingResult])
    func serverService(_ service: ServerService, didEncounterError error: ServerServiceError)

}

/**
 * NAME: ServerService
 *
 * DESCRIPTION:
 *     Application layer server service providing high-level server management.
 *     Encapsulates ServerManager and provides simplified business interfaces.
 *     Handles configuration management, state caching, and event forwarding.
 *
 * PROPERTIES:
 *     serverManager - Domain layer server manager
 *     configuration - Service configuration
 *     delegate - Service delegate for event notifications
 *     logger - Logger instance
 *     isStarted - Service running state
 *     lastServerList - Cached server list
 *     lastPingResults - Cached ping results
 */
public actor ServerService: ServiceLifecycle {
    // MARK: - Dependencies

    private let _serverManager: ServerManager
    private let configuration: ServerServiceConfiguration
    private let logger: LoggerProtocol
    private let lifecycleManager: ServiceLifecycleManager

    // MARK: - Public Access

    /// Public access to server manager for IP cache functionality
    public var serverManager: ServerManager {
        return _serverManager
    }

    // MARK: - State Management

    private var lastServerList: [ServerInfo] = []
    private var lastPingResults: [String: PingResult] = [:]
    private var lastUpdateTime: Date = Date.distantPast
    private var lastServerListVersion: String?

    // MARK: - Background Tasks

    private var backgroundPingTask: Task<Void, Never>?

    // MARK: - Delegate and Callbacks

    private weak var delegate: ServerServiceDelegate?

    // MARK: - ServiceLifecycle Protocol

    public let serviceName: String = "ServerService"
    
    // MARK: - Initialization
    
    /**
     * NAME: init
     *
     * DESCRIPTION:
     *     Initializes server service with dependencies and configuration.
     *
     * PARAMETERS:
     *     serverManager - Domain layer server manager
     *     configuration - Service configuration
     *     logger - Logger instance
     */
    public init(
        serverManager: ServerManager,
        configuration: ServerServiceConfiguration = .default,
        logger: LoggerProtocol
    ) {
        self._serverManager = serverManager
        self.configuration = configuration
        self.logger = logger
        self.lifecycleManager = ServiceLifecycleManager(serviceName: "ServerService", logger: logger)

        logger.info("Server service initialized", metadata: [
            "auto_select": "\(configuration.autoSelectBestServer)",
            "background_ping": "\(configuration.monitoring.enabled)",
            "cache_timeout": "\(configuration.serverListCacheTimeout)"
        ])
    }
    
    // MARK: - ServiceLifecycle Protocol Implementation

    public func getCurrentState() async -> ServiceState {
        return await lifecycleManager.getCurrentState()
    }

    public func isStarted() async -> Bool {
        return await lifecycleManager.isStarted()
    }

    /**
     * NAME: start
     *
     * DESCRIPTION:
     *     Starts server service and underlying server manager.
     *     Sets up callbacks and begins background operations.
     *
     * THROWS:
     *     ServerServiceError - If start operation fails
     */
    public func start() async throws {
        try await lifecycleManager.startTransition()

        do {
            // Start underlying server manager
            try await _serverManager.start()

            // Register callbacks
            await setupServerManagerCallbacks()

            // Start background ping task
            startBackgroundPingTask()

            await lifecycleManager.completeStart()

        } catch let error as ServerManagerError {
            await lifecycleManager.failStart(error)
            throw ServerServiceError.serverManagerError(error)
        } catch {
            await lifecycleManager.failStart(error)
            throw ServerServiceError.serverManagerError(.updateFailed(error.localizedDescription))
        }
    }
    
    /**
     * NAME: stop
     *
     * DESCRIPTION:
     *     Stops server service and cleans up resources.
     */
    public func stop() async throws {
        try await lifecycleManager.stopTransition()

        // Stop background ping task
        backgroundPingTask?.cancel()
        backgroundPingTask = nil

        // Stop server manager
        await _serverManager.stop()

        // Clear callbacks
        await _serverManager.clearCallbacks()

        // Reset state
        lastServerList.removeAll()
        lastPingResults.removeAll()
        lastUpdateTime = Date.distantPast

        await lifecycleManager.completeStop()
    }

    /**
     * NAME: restart
     *
     * DESCRIPTION:
     *     Restarts the server service.
     */
    public func restart() async throws {
        if await isStarted() {
            try await stop()
        }
        try await start()
    }
    
    // MARK: - Delegate Management
    
    /**
     * NAME: setDelegate
     *
     * DESCRIPTION:
     *     Sets service delegate for event notifications.
     *
     * PARAMETERS:
     *     delegate - Service delegate
     */
    public func setDelegate(_ delegate: ServerServiceDelegate?) {
        logger.info("Setting server service delegate")
        self.delegate = delegate
        logger.info("Server service delegate set successfully")
    }
    
    // MARK: - Server List Management
    
    /**
     * NAME: getServerList
     *
     * DESCRIPTION:
     *     Returns current server list with caching support.
     *     Uses cached data if within timeout, otherwise fetches fresh data.
     *
     * RETURNS:
     *     [ServerInfo] - Current server list
     *
     * THROWS:
     *     ServerServiceError - If operation fails
     */
    public func getServerList() async throws -> [ServerInfo] {
        try await ensureServiceStarted()

        // Check cache validity
        let cacheAge = Date().timeIntervalSince(lastUpdateTime)
        if cacheAge < configuration.serverListCacheTimeout && !lastServerList.isEmpty {
            // logger.debug("Returning cached server list", metadata: [
            //     "cache_age": "\(cacheAge)",
            //     "server_count": "\(lastServerList.count)"
            // ]) // Debug log commented for production
            return lastServerList
        }

        // Fetch fresh server list
        let servers = await _serverManager.getServers()
        lastServerList = servers
        lastUpdateTime = Date()

        logger.info("Server list retrieved", metadata: [
            "server_count": "\(servers.count)",
            "cache_updated": "true"
        ])

        return servers
    }

    /**
     * NAME: getFreshServerList
     *
     * DESCRIPTION:
     *     Returns current server list bypassing cache.
     *     Always fetches fresh data from ServerManager.
     *     Used when we need the latest ping results immediately.
     *
     * RETURNS:
     *     [ServerInfo] - Fresh server list with latest ping data
     *
     * THROWS:
     *     ServerServiceError - If operation fails
     */
    public func getFreshServerList() async throws -> [ServerInfo] {
        try await ensureServiceStarted()

        // Always fetch fresh data from ServerManager
        let servers = await _serverManager.getServers()

        // Update cache with fresh data
        lastServerList = servers
        lastUpdateTime = Date()

        // logger.debug("Fresh server list retrieved", metadata: [
        //     "server_count": "\(servers.count)",
        //     "cache_bypassed": "true"
        // ]) // Debug log commented for production

        return servers
    }
    
    /**
     * NAME: updateServerList
     *
     * DESCRIPTION:
     *     Updates server list with new data.
     *
     * PARAMETERS:
     *     servers - New server list
     *
     * THROWS:
     *     ServerServiceError - If update operation fails
     */
    public func updateServerList(_ servers: [ServerInfo]) async throws {
        try await ensureServiceStarted()

        guard !servers.isEmpty else {
            throw ServerServiceError.configurationInvalid("Empty server list provided")
        }

        logger.info("Updating server list", metadata: [
            "new_count": "\(servers.count)",
            "old_count": "\(lastServerList.count)"
        ])

        do {
            try await _serverManager.updateServerList(servers)
            lastServerList = servers
            lastUpdateTime = Date()

            // Notify delegate
            delegate?.serverService(self, didUpdateServerList: servers)
        } catch let error as ServerManagerError {
            logger.error("Failed to update server list", metadata: ["error": "\(error)"])
            throw ServerServiceError.serverManagerError(error)
        } catch {
            logger.error("Failed to update server list", metadata: ["error": "\(error)"])
            throw ServerServiceError.configurationInvalid("Update failed: \(error.localizedDescription)")
        }
    }

    /**
     * NAME: updateServerListWithVersion
     *
     * DESCRIPTION:
     *     Updates server list with version checking to avoid unnecessary UI updates.
     *     Only updates if version has changed from the last known version.
     *
     * PARAMETERS:
     *     servers - New server list
     *     version - Server list version string
     *
     * THROWS:
     *     ServerServiceError - If update operation fails
     */
    public func updateServerListWithVersion(_ servers: [ServerInfo], version: String) async throws {
        try await ensureServiceStarted()

        guard !servers.isEmpty else {
            throw ServerServiceError.configurationInvalid("Empty server list provided")
        }

        // Check if version has changed to avoid unnecessary UI updates
        let previousVersion = lastServerListVersion

        if let previousVersion = previousVersion, previousVersion == version {
            logger.info("Server list version unchanged, skipping UI update", metadata: [
                "version": version,
                "server_count": "\(servers.count)"
            ])
            return
        }

        logger.info("Updating server list with version checking (UI update deferred to next ping)", metadata: [
            "new_count": "\(servers.count)",
            "old_count": "\(lastServerList.count)",
            "version": version,
            "previous_version": previousVersion ?? "none",
            "version_changed": "\(previousVersion != version)"
        ])

        do {
            try await updateServerListSilently(servers)
            lastServerListVersion = version

        } catch let error as ServerManagerError {
            logger.error("Failed to update server list with version", metadata: ["error": "\(error)"])
            throw ServerServiceError.serverManagerError(error)
        } catch {
            logger.error("Failed to update server list with version", metadata: ["error": "\(error)"])
            throw ServerServiceError.configurationInvalid("Update with version failed: \(error.localizedDescription)")
        }
    }

    /**
     * NAME: updateServerListSilently
     *
     * DESCRIPTION:
     *     Updates server list without triggering delegate notifications.
     *     Used when server list version changes but we want to defer UI updates until next ping.
     *
     * PARAMETERS:
     *     servers - New server list
     *
     * THROWS:
     *     ServerServiceError - If update operation fails
     */
    private func updateServerListSilently(_ servers: [ServerInfo]) async throws {
        try await ensureServiceStarted()

        guard !servers.isEmpty else {
            throw ServerServiceError.configurationInvalid("Empty server list provided")
        }

        logger.info("Updating server list silently", metadata: [
            "new_count": "\(servers.count)",
            "old_count": "\(lastServerList.count)"
        ])

        do {
            try await _serverManager.updateServerListSilently(servers)
            lastServerList = servers
            lastUpdateTime = Date()

            // Note: Do NOT notify delegate to avoid immediate UI updates

        } catch let error as ServerManagerError {
            logger.error("Failed to update server list silently", metadata: ["error": "\(error)"])
            throw ServerServiceError.serverManagerError(error)
        } catch {
            logger.error("Failed to update server list silently", metadata: ["error": "\(error)"])
            throw ServerServiceError.configurationInvalid("Silent update failed: \(error.localizedDescription)")
        }
    }

    // MARK: - Server Testing and Selection

    /**
     * NAME: pingAllServers
     *
     * DESCRIPTION:
     *     Tests latency for all servers to help with server selection.
     *     Results are cached and used for best server selection.
     *     Timeout servers are reported with latency 0 (unreachable).
     *     This method never throws exceptions.
     */
    public func pingAllServers() async {
        logger.info("Starting pingAllServers operation")
        // print("🔍 [PING_DEBUG] ServerService.pingAllServers() - START")

        do {
            try await ensureServiceStarted()
            // print("🔍 [PING_DEBUG] ServerService.ensureServiceStarted() completed")
        } catch {
            logger.error("Service startup failed", metadata: ["error": error.localizedDescription])
            // print("🔍 [PING_DEBUG] ServerService.ensureServiceStarted() FAILED: \(error.localizedDescription)")
            return
        }

        // print("🔍 [PING_DEBUG] About to call _serverManager.pingAllServers()")
        await _serverManager.pingAllServers()
        // print("🔍 [PING_DEBUG] _serverManager.pingAllServers() completed")

        // Get updated ping results
        let pingResults = await _serverManager.getPingResults()
        lastPingResults = pingResults
        // print("🔍 [PING_DEBUG] Got ping results - count: \(pingResults.count)")

        // Log ping results summary
        // for (serverID, result) in pingResults {
        //     print("🔍 [PING_DEBUG] ServerService ping result - server: \(serverID), latency: \(result.latency)ms, success: \(result.isSuccess)")
        // }

        // Notify delegate
        // print("🔍 [PING_DEBUG] About to notify delegate with ping results")
        // print("🔍 [PING_DEBUG] Delegate is: \(delegate != nil ? "NOT NIL" : "NIL")")
        if let delegate = delegate {
            // print("🔍 [PING_DEBUG] Calling delegate.serverService(didCompletePing:)")
            delegate.serverService(self, didCompletePing: pingResults)
            // print("🔍 [PING_DEBUG] Delegate.serverService(didCompletePing:) call completed")
        } else {
            // print("🔍 [PING_DEBUG] ERROR: Delegate is nil, cannot notify ping completion!")
        }
        // print("🔍 [PING_DEBUG] Delegate notification completed")

        // print("🔍 [PING_DEBUG] ServerService.pingAllServers() - COMPLETED")
    }

    /**
     * NAME: getPingResults
     *
     * DESCRIPTION:
     *     Returns cached ping results for all servers.
     *
     * RETURNS:
     *     [String: PingResult] - Ping results by server ID
     */
    public func getPingResults() async -> [String: PingResult] {
        guard await lifecycleManager.isStarted() else {
            return [:]
        }

        return lastPingResults
    }

    /**
     * NAME: selectServer
     *
     * DESCRIPTION:
     *     Selects server by ID with validation and error handling.
     *
     *     IMPORTANT: On iOS/macOS, server switching requires NetworkExtension rebuild.
     *     This is different from Go backend which maintains persistent TUN device.
     *     The actual NE rebuild will be handled by upper layers (VPNService/Platform Layer).
     *
     * PARAMETERS:
     *     serverID - Server ID to select
     *
     * THROWS:
     *     ServerServiceError - If selection fails
     */
    public func selectServer(serverID: String) async throws {
        try await ensureServiceStarted()

        logger.info("Selecting server", metadata: [
            "server_id": serverID
        ])

        do {
            try await _serverManager.selectServer(serverID: serverID)

            // Get selected server info
            let selectedServer = await _serverManager.getCurrentServer()

            // Notify delegate - upper layers should handle NE rebuild
            delegate?.serverService(self, didSelectServer: selectedServer)

            logger.info("Server selected successfully", metadata: [
                "server_id": serverID,
                "server_name": selectedServer?.name ?? "unknown"
            ])

        } catch let error as ServerManagerError {
            let serviceError = ServerServiceError.serverSelectionFailed(error.localizedDescription)
            delegate?.serverService(self, didEncounterError: serviceError)
            throw serviceError
        }
    }
    
    /**
     * NAME: selectBestServer
     *
     * DESCRIPTION:
     *     Selects best available server based on current selection mode.
     *
     *     IMPORTANT: On iOS/macOS, server switching requires NetworkExtension rebuild.
     *     Unlike Go backend's persistent TUN device, iOS/macOS must rebuild NE with
     *     new tunnelRemoteAddress. Switch time: ~2-5 seconds vs Go's ~100-500ms.
     *
     * RETURNS:
     *     ServerInfo? - Selected server or nil if none available
     *
     * THROWS:
     *     ServerServiceError - If selection fails
     */
    public func selectBestServer() async throws -> ServerInfo? {
        try await ensureServiceStarted()

        logger.info("Selecting best server from available servers")

        // Check if we have servers
        let serverCount = lastServerList.count

        if serverCount == 0 {
            logger.error("No servers available for selection")
            throw ServerServiceError.noServersConfigured
        }

        do {
            let bestServer = try await _serverManager.selectBestServer()

            // Notify delegate - upper layers should handle NE rebuild
            delegate?.serverService(self, didSelectServer: bestServer)

            if let server = bestServer {
                logger.info("Best server selected", metadata: [
                    "server_id": server.id,
                    "server_name": server.name,
                    "ping": "\(server.ping)"
                ])
            } else {
                logger.warning("No best server available")
            }

            return bestServer

        } catch let error as ServerManagerError {
            let serviceError = ServerServiceError.serverSelectionFailed(error.localizedDescription)
            delegate?.serverService(self, didEncounterError: serviceError)
            throw serviceError
        }
    }
    
    /**
     * NAME: getCurrentServer
     *
     * DESCRIPTION:
     *     Returns currently selected server.
     *
     * RETURNS:
     *     ServerInfo? - Current server or nil
     */
    public func getCurrentServer() async -> ServerInfo? {
        guard await lifecycleManager.isStarted() else {
            return nil
        }

        return await _serverManager.getCurrentServer()
    }

    /**
     * NAME: getAllServersForExclusion
     *
     * DESCRIPTION:
     *     Returns all server IPs for NetworkExtension excludedRoutes configuration.
     *     Delegates to ServerManager to get resolved IP addresses from cache.
     *
     *     IMPORTANT: iOS/macOS requires all server IPs to be excluded from VPN routing
     *     to prevent routing loops. This is configured statically in NEPacketTunnelNetworkSettings
     *     unlike Go backend's dynamic route management.
     *
     * RETURNS:
     *     [String] - Array of resolved server IP addresses for exclusion
     *
     * THROWS:
     *     ServerServiceError - If operation fails
     */
    public func getAllServersForExclusion() async throws -> [String] {
        try await ensureServiceStarted()

        // Get all cached server IPs from ServerManager
        let cachedServerIPs = await _serverManager.getAllCachedServerIPs()

        // If we have cached IPs, use them
        if !cachedServerIPs.isEmpty {
            logger.info("Retrieved cached server IPs for NE exclusion", metadata: [
                "cached_ip_count": "\(cachedServerIPs.count)",
                "platform_note": "Using ServerManager cached IPs for iOS/macOS excludedRoutes"
            ])
            return cachedServerIPs
        }

        // Fallback: trigger server list refresh and IP resolution
        logger.warning("No cached server IPs found, triggering server list refresh")
        let servers = try await getServerList()

        // Trigger IP resolution for all servers in background
        Task {
            for server in servers {
                _ = await _serverManager.resolveServerIP(server.serverName)
            }
        }

        // Return hostnames as fallback (NetworkExtension will attempt resolution)
        let serverHostnames = servers.map { $0.serverName }
        logger.warning("Returning hostnames as fallback for NE exclusion", metadata: [
            "server_count": "\(servers.count)",
            "fallback_note": "NetworkExtension will attempt DNS resolution"
        ])

        return serverHostnames
    }

    /**
     * NAME: resolveServerIP
     *
     * DESCRIPTION:
     *     Resolves server hostname to IP address with caching.
     *     Delegates to ServerManager for actual resolution.
     *
     * PARAMETERS:
     *     hostname - Server hostname to resolve
     *
     * RETURNS:
     *     String? - Resolved IP address or nil if failed
     */
    public func resolveServerIP(_ hostname: String) async -> String? {
        return await _serverManager.resolveServerIP(hostname)
    }

    /**
     * NAME: getAllCachedServerIPs
     *
     * DESCRIPTION:
     *     Gets all cached server IP addresses.
     *     Delegates to ServerManager for actual cache access.
     *
     * RETURNS:
     *     [String] - Array of cached server IP addresses
     */
    public func getAllCachedServerIPs() async -> [String] {
        return await _serverManager.getAllCachedServerIPs()
    }


    // MARK: - Private Helper Methods
    
    /**
     * NAME: ensureServiceStarted
     *
     * DESCRIPTION:
     *     Ensures service is started before operations.
     *
     * THROWS:
     *     ServerServiceError.serviceNotStarted - If service not started
     */
    private func ensureServiceStarted() async throws {
        guard await lifecycleManager.isStarted() else {
            throw ServerServiceError.serviceNotStarted
        }
    }
    
    /**
     * NAME: setupServerManagerCallbacks
     *
     * DESCRIPTION:
     *     Sets up callbacks from server manager to forward events.
     */
    private func setupServerManagerCallbacks() async {
        // Server list update callback
        await _serverManager.registerUpdateCallback { [weak self] servers in
            Task {
                guard let self = self else { return }
                await self.handleServerListUpdate(servers)
            }
        }

        // Server status change callback
        await _serverManager.registerStatusCallback { [weak self] serverID, status in
            Task {
                guard let self = self else { return }
                await self.handleServerStatusChange(serverID: serverID, status: status)
            }
        }
        

    }
    
    /**
     * NAME: handleServerListUpdate
     *
     * DESCRIPTION:
     *     Handles server list update from server manager.
     *
     * PARAMETERS:
     *     servers - Updated server list
     */
    private func handleServerListUpdate(_ servers: [ServerInfo]) async {
        lastServerList = servers
        lastUpdateTime = Date()
        
        // logger.debug("Server list updated via callback", metadata: [
        //     "server_count": "\(servers.count)"
        // ]) // Debug log commented for production
        
        delegate?.serverService(self, didUpdateServerList: servers)
    }
    
    /**
     * NAME: handleServerStatusChange
     *
     * DESCRIPTION:
     *     Handles server status change from server manager.
     *
     * PARAMETERS:
     *     serverID - Server ID that changed
     *     status - New server status
     */
    private func handleServerStatusChange(serverID: String, status: ServerStatus) async {
        // logger.debug("Server status changed", metadata: [
        //     "server_id": serverID,
        //     "status": status.rawValue
        // ]) // Debug log commented for production
        
        delegate?.serverService(self, didChangeServerStatus: serverID, status: status)
    }
    


    // MARK: - Background Ping Task

    /**
     * NAME: startBackgroundPingTask
     *
     * DESCRIPTION:
     *     Starts background ping monitoring task.
     *     This ensures all ping operations go through ServerService and trigger delegate notifications.
     */
    private func startBackgroundPingTask() {
        logger.info("Starting background ping task", metadata: [
            "ping_interval": "\(configuration.pingInterval)"
        ])

        backgroundPingTask = Task { [weak self] in
            var pingCycle = 0
            while !Task.isCancelled {
                guard let self = self else {
                    break
                }

                // Check if service is still running
                guard await self.lifecycleManager.isStarted() else {
                    // await self.logger.info("Background ping task stopping - service not running") // Debug log commented for production
                    break
                }

                pingCycle += 1
                // await self.logger.info("Background ping cycle #\(pingCycle) started") // Debug log commented for production

                await self.pingAllServers()
                // await self.logger.info("Background ping cycle #\(pingCycle) completed in \(duration) seconds") // Debug log commented for production
                try? await Task.sleep(nanoseconds: UInt64(self.configuration.pingInterval * 1_000_000_000))
            }

            // await self?.logger.info("Background ping task ended") // Debug log commented for production
        }
    }
}
