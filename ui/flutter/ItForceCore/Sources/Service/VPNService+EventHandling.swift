/*******************************************************************************
 * LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
 *
 * This source code is confidential, proprietary, and contains trade
 * secrets that are the sole property of UNISASE Corporation.
 * Copy and/or distribution of this source code or disassembly or reverse
 * engineering of the resultant object code are strictly forbidden without
 * the written consent of UNISASE Corporation LLC.
 *
 *******************************************************************************
 * FILE NAME :      VPNService+EventHandling.swift
 *
 * DESCRIPTION :    Event handling and delegate management extension for VPNService.
 *                  Contains optimized event processing logic with eliminated transparent
 *                  pass-through methods and unified delegate notification patterns.
 *                  Migrated from VPNService.swift to improve code organization.
 *
 * ARCHITECTURE :   - Unified state management using VPNState enum
 *                  - Eliminated transparent pass-through methods for efficiency
 *                  - Standardized error handling with unified patterns
 *                  - Simplified thread safety with consistent delegate notification
 *                  - Optimized ServerService delegate with direct forwarding
 *
 * AUTHOR :         wei
 *
 * HISTORY :        03/07/2025 create - Migrated and optimized from VPNService.swift
 ******************************************************************************/

import Foundation
import NetworkExtension
import OSLog

// MARK: - VPNService Event Handling Extension

extension VPNService {
    
    // MARK: - Unified Delegate Notification
    // Note: notifyDelegate method is defined in main VPNService.swift file

    // MARK: - Unified State Management Helpers



    /**
     * NAME: handleVPNServiceError
     *
     * DESCRIPTION:
     *     Unified error handling method for VPN service errors.
     *     Provides consistent error logging and delegate notification.
     *
     * PARAMETERS:
     *     error - VPN service error to handle
     *     context - Additional context for logging
     */
    private func handleVPNServiceError(_ error: VPNServiceError, context: String = "") async {
        let contextInfo = context.isEmpty ? "" : " (\(context))"
        logger.warning("VPN service error\(contextInfo)", metadata: [
            "error": error.localizedDescription
        ])

        updateVPNState(.error(error))
        notifyDelegate { $0.vpnService(self, didEncounterError: error) }
    }
    
    // MARK: - Connection Event Handlers
    


    /**
     * NAME: handleConnectionEstablished
     *
     * DESCRIPTION:
     *     Handles successful connection establishment.
     *
     * PARAMETERS:
     *     server - Connected server information
     */
    internal func handleConnectionEstablished(_ server: ServerInfo) async {
        logger.info("VPN connection established", metadata: [
            "server_id": server.id,
            "server_name": server.name
        ])

        updateVPNState(.connected(server: server, since: Date()))
        notifyDelegate { $0.vpnService(self, didConnectToServer: server) }
    }

    /**
     * NAME: handleConnectionFailed
     *
     * DESCRIPTION:
     *     Handles connection failure with unified error handling.
     *
     * PARAMETERS:
     *     error - Connection manager error
     */
    internal func handleConnectionFailed(_ error: ConnectionManagerError) async {
        let vpnError = VPNServiceError.connectionManagerError(error)
        await handleVPNServiceError(vpnError, context: "connection failed")
    }

    /**
     * NAME: handleConnectionDisconnected
     *
     * DESCRIPTION:
     *     Handles connection disconnection with unified disconnect coordination.
     *     Uses unified disconnect mechanism to ensure state consistency.
     *
     * PARAMETERS:
     *     reason - Disconnection reason
     */
    internal func handleConnectionDisconnected(_ reason: String?) async {
        logger.info("VPN connection disconnected", metadata: [
            "reason": reason ?? "user_initiated"
        ])

        // Use unified disconnect coordination (state validation handled in updateVPNState)
        let disconnectReason: VPNDisconnectReason = reason == "user_initiated" ? .userInitiated : .connectionLost
        await performUnifiedDisconnect(
            reason: disconnectReason,
            source: .connectionManager
        )
    }
    
    // MARK: - Heartbeat Event Handlers (moved to VPNService+Monitoring.swift)
    // handleHeartbeatReceived() method has been moved to VPNService+Monitoring.swift for better code organization

    // handleHeartbeatTimeout method removed - heartbeat timeout is now handled by ConnectionManager
    // through the callback mechanism in handleReconnectRequest
    
    // MARK: - Network Event Handlers (Simplified)

    // MARK: - Network Change Handling (Removed)
    // Network change detection is now exclusively handled by ConnectionManager's NWPathMonitor
    // All legacy network change methods have been removed for full replacement architecture
    
    // MARK: - Platform Event Handlers
    
    /**
     * NAME: handleNetworkExtensionRebuildRequired
     *
     * DESCRIPTION:
     *     Handles NetworkExtension rebuild requirement for iOS/macOS.
     *
     * PARAMETERS:
     *     server - Target server requiring NE rebuild
     */
    internal func handleNetworkExtensionRebuildRequired(_ server: ServerInfo) async {
        logger.info("NetworkExtension rebuild required", metadata: [
            "target_server": server.id,
            "platform_note": "iOS/macOS server switch requirement"
        ])

        notifyDelegate { $0.vpnService(self, didRequireNetworkExtensionRebuild: server) }
    }
    
    // MARK: - Server Event Handlers (Optimized)
    
    /**
     * NAME: handlePingCompletion
     *
     * DESCRIPTION:
     *     Handles ping completion with simplified delegate notification.
     *     Replaces complex MainActor handling with unified pattern.
     *
     * PARAMETERS:
     *     results - Ping results by server ID
     */
    internal func handlePingCompletion(_ results: [String: PingResult]) async {
        // print("🔍 [PING_DEBUG] VPNService.handlePingCompletion() - CALLED with \(results.count) results")

        // logger.info("Ping operation completed") // Debug log commented for production

        // Log individual ping results for debugging (standardized format)
        // for (serverID, result) in results {
        //     logger.debug("Ping result", metadata: [
        //         "server_id": serverID,
        //         "latency_ms": "\(result.latency)",
        //         "success": "\(result.isSuccess)"
        //     ])
        // } // Debug log commented for production

        // Simplified delegate notification
        // print("🔍 [PING_DEBUG] About to call notifyDelegate for ping completion")
        notifyDelegate { $0.vpnService(self, didCompletePing: results) }
        // print("🔍 [PING_DEBUG] notifyDelegate call completed for ping completion")
    }

    /**
     * NAME: handleServerServiceError
     *
     * DESCRIPTION:
     *     Handles errors from ServerService with unified error conversion.
     *
     * PARAMETERS:
     *     error - Server service error
     */
    internal func handleServerServiceError(_ error: ServerServiceError) async {
        let vpnError = VPNServiceError.serverServiceError(error)
        await handleVPNServiceError(vpnError, context: "server service error")
    }
    
    // MARK: - Traffic Monitoring (Simplified)
    
    /**
     * NAME: handleTrafficUpdate
     *
     * DESCRIPTION:
     *     Handles traffic statistics update with minimal processing.
     *
     * PARAMETERS:
     *     stats - Traffic statistics
     */
    internal func handleTrafficUpdate(_ stats: TrafficStatistics) async {
        lastTrafficUpdate = Date()
        notifyDelegate { $0.vpnService(self, didUpdateTraffic: stats) }
    }

    // MARK: - Delegate Factory

    /**
     * NAME: createServerServiceDelegate
     *
     * DESCRIPTION:
     *     Creates optimized ServerService delegate instance.
     *
     * RETURNS:
     *     ServerServiceDelegate - Optimized delegate implementation
     */
    internal func createServerServiceDelegate() -> ServerServiceDelegate {
        return ServerServiceDelegateImpl(vpnService: self)
    }
}

// MARK: - Optimized ServerService Delegate Implementation

/**
 * NAME: ServerServiceDelegateImpl
 *
 * DESCRIPTION:
 *     Optimized delegate implementation for ServerService callbacks.
 *     Eliminates unnecessary transparent pass-through methods and provides
 *     direct delegate forwarding for improved efficiency.
 */
private final class ServerServiceDelegateImpl: ServerServiceDelegate, @unchecked Sendable {
    private weak var vpnService: VPNService?

    init(vpnService: VPNService) {
        self.vpnService = vpnService
    }

    // MARK: - Direct Delegate Forwarding (Eliminated Transparent Pass-through)

    /**
     * Server list updates are forwarded directly to final delegate
     * Eliminates: ServerService -> VPNService.handleServerListUpdate -> VPNServiceDelegate
     * Optimized: ServerService -> VPNServiceDelegate (direct)
     */
    func serverService(_ service: ServerService, didUpdateServerList servers: [ServerInfo]) {
        Task { @MainActor in
            vpnService?.delegate?.vpnService(vpnService!, didUpdateServerList: servers)
        }
    }

    /**
     * Server selection events are logged but not forwarded (no business value)
     * Eliminates: Unnecessary transparent pass-through method
     */
    func serverService(_ service: ServerService, didSelectServer server: ServerInfo?) {
        // Eliminated transparent pass-through - no business logic needed
        guard server != nil, vpnService != nil else { return }
        // Debug log commented for production
    }

    /**
     * Server status changes are logged but not forwarded (no delegate method)
     * Eliminates: Unnecessary transparent pass-through method
     */
    func serverService(_ service: ServerService, didChangeServerStatus serverID: String, status: ServerStatus) {
        // Eliminated transparent pass-through - no delegate notification needed
        guard vpnService != nil else { return }
        // Debug log commented for production
    }

    /**
     * Ping completion requires business logic processing
     * Maintains: ServerService -> VPNService.handlePingCompletion (has business logic)
     */
    func serverService(_ service: ServerService, didCompletePing results: [String: PingResult]) {
        // print("🔍 [PING_DEBUG] VPNServiceDelegate.serverService(didCompletePing:) - CALLED")
        // print("🔍 [PING_DEBUG] VPNService is: \(vpnService != nil ? "NOT NIL" : "NIL")")
        Task {
            // print("🔍 [PING_DEBUG] About to call vpnService.handlePingCompletion()")
            await vpnService?.handlePingCompletion(results)
            // print("🔍 [PING_DEBUG] vpnService.handlePingCompletion() completed")
        }
    }

    /**
     * Server service errors require error conversion
     * Maintains: ServerService -> VPNService.handleServerServiceError (has business logic)
     */
    func serverService(_ service: ServerService, didEncounterError error: ServerServiceError) {
        Task {
            await vpnService?.handleServerServiceError(error)
        }
    }


}
