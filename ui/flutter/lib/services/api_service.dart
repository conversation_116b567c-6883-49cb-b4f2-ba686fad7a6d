/// LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
///
/// This source code is confidential, proprietary, and contains trade
/// secrets that are the sole property of UNISASE Corporation.
/// Copy and/or distribution of this source code or disassembly or reverse
/// engineering of the resultant object code are strictly forbidden without
/// the written consent of UNISASE Corporation LLC.
///
/// FILE NAME :      api_service.dart
///
/// DESCRIPTION :    API服务层，负责与后端服务器的HTTP通信，包括用户认证、
///                  服务器管理、连接控制、状态查询等核心API功能
///
/// AUTHOR :         wei
///
/// HISTORY :        10/06/2025 create

import 'dart:async';

import 'package:flutter/foundation.dart';

import '../utils/api_exception.dart';
import '../utils/constants.dart';
import '../models/server.dart';
import '../models/routing_settings.dart';

import '../services/log_service.dart';
import 'platform/platform_service_factory.dart';
import 'platform/cross_platform_api_service.dart';
import 'platform/platform_channel_api_service.dart';
import 'platform/http_api_service.dart';

/// ApiService
///
/// PURPOSE:
///     API服务层，负责与后端服务器的HTTP通信，提供统一的API接口
///
/// FEATURES:
///     - 用户认证：登录验证和会话管理
///     - 服务器管理：获取服务器列表、测试延迟
///     - 连接控制：建立和断开VPN连接
///     - 状态查询：获取连接状态和接口信息
///     - 路由设置：配置路由模式和自定义路由
///     - 健康检查：监控后端服务状态
///     - 错误处理：统一的异常处理和超时管理
///
/// USAGE:
///     通过依赖注入获取实例，调用相应的API方法进行后端通信
class ApiService {
  final String baseUrl;
  LogService? _logService;
  CrossPlatformApiService? _platformApiService;
  bool _isInitialized = false;



  /// ApiService构造函数
  ///
  /// DESCRIPTION:
  ///     创建API服务实例，自动检测平台并使用相应的实现
  ///
  /// PARAMETERS:
  ///     baseUrl - API基础URL地址（仅用于Windows/Linux平台）
  ApiService({required this.baseUrl});

  /// logService getter
  ///
  /// DESCRIPTION:
  ///     获取日志服务实例，如果未设置则创建默认实例
  ///
  /// RETURNS:
  ///     LogService - 日志服务实例
  LogService get logService {
    _logService ??= LogService();
    return _logService!;
  }

  /// logService setter
  ///
  /// DESCRIPTION:
  ///     设置日志服务实例
  ///
  /// PARAMETERS:
  ///     service - 日志服务实例
  set logService(LogService service) {
    _logService = service;
  }

  /// initialize
  ///
  /// DESCRIPTION:
  ///     初始化API服务，根据平台自动选择实现方式
  ///
  /// RETURNS:
  ///     Future<bool> - 初始化成功返回true，失败返回false
  Future<bool> initialize() async {
    if (_isInitialized) {
      return true;
    }

    try {
      _platformApiService = PlatformServiceFactory.createApiService();
      final success = await _platformApiService!.initialize();
      if (success) {
        _isInitialized = true;
        logService.info('ApiService', 'Platform API service initialized successfully');
      }
      return success;
    } catch (e) {
      logService.error('ApiService', 'Failed to initialize platform API service: $e');
      return false;
    }
  }

  /// dispose
  ///
  /// DESCRIPTION:
  ///     释放API服务资源
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  Future<void> dispose() async {
    if (_platformApiService != null) {
      await _platformApiService!.dispose();
      _platformApiService = null;
    }
    _isInitialized = false;
  }

  /// _ensureInitialized
  ///
  /// DESCRIPTION:
  ///     确保API服务已初始化，如果未初始化则自动初始化
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  ///
  /// THROWS:
  ///     Exception - 初始化失败时抛出异常
  Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      final success = await initialize();
      if (!success) {
        throw Exception('Failed to initialize API service');
      }
    }
  }





  /// login
  ///
  /// DESCRIPTION:
  ///     用户登录认证
  ///
  /// PARAMETERS:
  ///     username - 用户名
  ///     password - 密码
  ///
  /// RETURNS:
  ///     Future<Map<String, dynamic>> - 登录响应数据
  ///
  /// THROWS:
  ///     ApiException - 登录失败时抛出异常
  Future<Map<String, dynamic>> login(String username, String password) async {
    await _ensureInitialized();

    try {
      final userInfo = await _platformApiService!.login(username, password);
      // Convert UserInfo back to Map for backward compatibility
      return userInfo.toJson();
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException('Login failed: ${e.toString()}', 1, 'unknown');
    }
  }

  /// getBestServerFromLoginResponse
  ///
  /// DESCRIPTION:
  ///     从登录响应中提取最佳服务器信息
  ///
  /// PARAMETERS:
  ///     loginResponse - 登录响应数据
  ///
  /// RETURNS:
  ///     Server? - 最佳服务器对象，如果不存在则返回null
  Server? getBestServerFromLoginResponse(Map<String, dynamic> loginResponse) {
    // 注意：loginResponse参数现在是UserInfo.toJson()的结果，不包含best_server
    // 我们需要从平台API服务获取原始登录响应
    debugPrint('🔍 getBestServerFromLoginResponse: Received UserInfo response = $loginResponse');

    Map<String, dynamic>? originalResponse;

    if (_platformApiService is PlatformChannelApiService) {
      final platformService = _platformApiService as PlatformChannelApiService;
      originalResponse = platformService.getLastLoginResponse();
      debugPrint('🔍 getBestServerFromLoginResponse: Original login response from PlatformChannel = $originalResponse');
    } else if (_platformApiService is HttpApiService) {
      final httpService = _platformApiService as HttpApiService;
      originalResponse = httpService.getLastLoginResponse();
      debugPrint('🔍 getBestServerFromLoginResponse: Original login response from HttpApi = $originalResponse');
    } else {
      debugPrint('🔍 getBestServerFromLoginResponse: ❌ Platform service is not supported type');
    }

    if (originalResponse != null) {
      final data = originalResponse['data'] as Map<String, dynamic>?;
      debugPrint('🔍 getBestServerFromLoginResponse: data = $data');

      if (data != null && data['best_server'] != null) {
        debugPrint('🔍 getBestServerFromLoginResponse: best_server = ${data['best_server']}');
        try {
          final server = Server.fromJson(data['best_server']);
          debugPrint('🔍 getBestServerFromLoginResponse: ✅ Successfully parsed server: ${server.name}');
          return server;
        } catch (e) {
          debugPrint('🔍 getBestServerFromLoginResponse: ❌ Failed to parse server: $e');
          return null;
        }
      } else {
        debugPrint('🔍 getBestServerFromLoginResponse: ❌ data is null or best_server is null');
      }
    } else {
      debugPrint('🔍 getBestServerFromLoginResponse: ❌ No original login response found');
    }

    return null;
  }

  /// getServers
  ///
  /// DESCRIPTION:
  ///     获取可用服务器列表
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<List<Server>> - 服务器列表
  ///
  /// THROWS:
  ///     ApiException - 获取服务器列表失败时抛出异常
  Future<List<Server>> getServers() async {
    await _ensureInitialized();

    try {
      return await _platformApiService!.getServers();
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException('Get server list failed: ${e.toString()}', 1, 'unknown');
    }
  }



  /// pingServers
  ///
  /// DESCRIPTION:
  ///     测试所有服务器的网络延迟
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  ///
  /// THROWS:
  ///     ApiException - 测试延迟失败时抛出异常
  Future<void> pingServers() async {
    logService.debug('ApiService', 'Starting pingServers operation');
    await _ensureInitialized();

    try {
      // Call platform-specific pingServers method which handles batch ping and events
      // print('🔍 [PING_DEBUG] ApiService.pingServers() - START');
      logService.debug('ApiService', 'Calling platform-specific pingServers method');
      // print('🔍 [PING_DEBUG] About to call _platformApiService.pingServers()');
      await _platformApiService!.pingServers();
      // print('🔍 [PING_DEBUG] _platformApiService.pingServers() completed successfully');
      logService.debug('ApiService', 'Platform-specific pingServers completed successfully');
      // print('🔍 [PING_DEBUG] ApiService.pingServers() - SUCCESS');
    } catch (e) {
      // print('🔍 [PING_DEBUG] ApiService.pingServers() - ERROR: ${e.toString()}');
      logService.error('ApiService', 'Platform-specific pingServers failed', e);
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException('Server ping failed: ${e.toString()}', 1, 'unknown');
    }
  }

  /// connect
  ///
  /// DESCRIPTION:
  ///     连接到指定的VPN服务器，传递用户名和密码进行认证
  ///     返回连接结果数据，包括interface信息（Windows平台）
  ///
  /// PARAMETERS:
  ///     serverId - 服务器ID
  ///     username - 用户名
  ///     password - 密码（Windows平台为密文，其他平台为明文）
  ///
  /// RETURNS:
  ///     Future<Map<String, dynamic>?> - 连接结果数据，包括interface信息
  ///
  /// THROWS:
  ///     ApiException - 连接失败时抛出异常
  Future<Map<String, dynamic>?> connect(String serverId, String username, String password) async {
    await _ensureInitialized();

    try {
      return await _platformApiService!.connect(serverId, username, password);
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException('Connection failed: ${e.toString()}', 1, 'unknown');
    }
  }

  /// disconnect
  ///
  /// DESCRIPTION:
  ///     断开当前的VPN连接
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  ///
  /// THROWS:
  ///     ApiException - 断开连接失败时抛出异常
  Future<void> disconnect() async {
    await _ensureInitialized();

    try {
      await _platformApiService!.disconnect();
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException('Disconnect failed: ${e.toString()}', 1, 'unknown');
    }
  }

  /// reconnect
  ///
  /// DESCRIPTION:
  ///     重新连接到VPN服务器，复用平台特定的重连逻辑
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  ///
  /// THROWS:
  ///     ApiException - 重连失败时抛出异常
  Future<void> reconnect() async {
    await _ensureInitialized();

    try {
      await _platformApiService!.reconnect();
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException('Reconnect failed: ${e.toString()}', 1, 'unknown');
    }
  }

  /// getStatus
  ///
  /// DESCRIPTION:
  ///     获取当前连接状态信息
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<Map<String, dynamic>> - 连接状态数据
  ///
  /// THROWS:
  ///     ApiException - 获取状态失败时抛出异常
  Future<Map<String, dynamic>> getStatus() async {
    await _ensureInitialized();

    try {
      final connectionStatus = await _platformApiService!.getConnectionStatus();
      // Convert ConnectionStatus enum to Map for backward compatibility
      return {
        'status': connectionStatus.toString().split('.').last,
        'connected': connectionStatus == ConnectionStatus.connected,
        'connecting': connectionStatus == ConnectionStatus.connecting,
        'disconnecting': connectionStatus == ConnectionStatus.disconnecting,
        'error': connectionStatus == ConnectionStatus.error,
      };
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException('Get status failed: ${e.toString()}', 1, 'unknown');
    }
  }

  /// healthCheck
  ///
  /// DESCRIPTION:
  ///     检查后端服务的健康状态
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<bool> - 服务是否健康
  Future<bool> healthCheck() async {
    // For uninitialized service, try to initialize first
    if (!_isInitialized) {
      try {
        return await initialize();
      } catch (e) {
        return false;
      }
    }

    try {
      return await _platformApiService!.healthCheck();
    } catch (e) {
      return false;
    }
  }



  /// shutdown
  ///
  /// DESCRIPTION:
  ///     关闭后端服务
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  Future<void> shutdown() async {
    await shutdownBackend();
  }

  /// shutdownBackend
  ///
  /// DESCRIPTION:
  ///     关闭后端服务，返回操作是否成功
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<bool> - 关闭操作是否成功
  Future<bool> shutdownBackend() async {
    if (!_isInitialized || _platformApiService == null) {
      return true; // Already shut down
    }

    try {
      await _platformApiService!.shutdown();
      await dispose();
      return true;
    } catch (e) {
      // Ignore errors, service might already be shut down
      logService.warning('ApiService', 'Shutdown error (may be expected): $e');
      return true;
    }
  }

  /// getInterfaceInfo
  ///
  /// DESCRIPTION:
  ///     获取网络接口信息
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<Map<String, dynamic>> - 接口信息数据
  ///
  /// THROWS:
  ///     ApiException - 获取接口信息失败时抛出异常
  Future<Map<String, dynamic>> getInterfaceInfo() async {
    await _ensureInitialized();

    try {
      // Debug: Log API call start
      logService.error('ApiService', 'Calling platform API service getInterfaceInfo');

      final interfaceInfo = await _platformApiService!.getInterfaceInfo();

      // Debug: Log received interface info
      logService.error('ApiService', 'Received interface info from platform API - '
          'interfaceName: ${interfaceInfo.interfaceName}, '
          'localIp: ${interfaceInfo.localIp}, '
          'tunIp: ${interfaceInfo.tunIp}');

      // Convert InterfaceInfo to Map for backward compatibility
      final result = interfaceInfo.toJson();

      // Debug: Log converted JSON
      logService.error('ApiService', 'Converted to JSON: $result');

      return result;
    } catch (e) {
      // Debug: Log API error
      logService.error('ApiService', 'getInterfaceInfo failed: $e');

      if (e is ApiException) {
        rethrow;
      }
      throw ApiException('Get interface info failed: ${e.toString()}', 1, 'unknown');
    }
  }

  /// getRoutingSettings
  ///
  /// DESCRIPTION:
  ///     获取当前路由设置
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<Map<String, dynamic>> - 路由设置数据
  ///
  /// THROWS:
  ///     ApiException - 获取路由设置失败时抛出异常
  Future<Map<String, dynamic>> getRoutingSettings() async {
    await _ensureInitialized();

    try {
      final routingSettings = await _platformApiService!.getRoutingSettings();
      // Convert RoutingSettingsModel to Map for backward compatibility
      return routingSettings.toJson();
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException('Get routing settings failed: ${e.toString()}', 1, 'unknown');
    }
  }

  /// setRoutingSettings
  ///
  /// DESCRIPTION:
  ///     设置路由配置
  ///
  /// PARAMETERS:
  ///     settings - 路由设置模型
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  ///
  /// THROWS:
  ///     ApiException - 设置路由失败时抛出异常
  Future<void> setRoutingSettings(RoutingSettingsModel settings) async {
    await _ensureInitialized();

    try {
      await _platformApiService!.updateRoutingSettings(settings);
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException('Set routing mode failed: ${e.toString()}', 1, 'unknown');
    }
  }

  /// setServerProviderUrl
  ///
  /// DESCRIPTION:
  ///     设置服务器列表提供商URL
  ///
  /// PARAMETERS:
  ///     url - 服务器列表URL
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  ///
  /// THROWS:
  ///     ApiException - 设置URL失败时抛出异常
  Future<void> setServerProviderUrl(String url) async {
    await _ensureInitialized();

    try {
      await _platformApiService!.setServerProviderUrl(url);
      logService.info('ApiService', 'Server provider URL set successfully: $url');
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException('Set server provider URL failed: ${e.toString()}', 1010, 'unknown');
    }
  }

  /// setDomain
  ///
  /// DESCRIPTION:
  ///     设置客户域（实际上是服务器列表URL的别名）
  ///
  /// PARAMETERS:
  ///     domain - 客户域URL
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  ///
  /// THROWS:
  ///     ApiException - 设置域失败时抛出异常
  Future<void> setDomain(String domain) async {
    // setDomain is an alias for setServerProviderUrl for backward compatibility
    await setServerProviderUrl(domain);
  }


}
