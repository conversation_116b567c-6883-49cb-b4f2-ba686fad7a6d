/// LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
///
/// This source code is confidential, proprietary, and contains trade
/// secrets that are the sole property of UNISASE Corporation.
/// Copy and/or distribution of this source code or disassembly or reverse
/// engineering of the resultant object code are strictly forbidden without
/// the written consent of UNISASE Corporation LLC.
///
/// FILE NAME :      connection_manager.dart
///
/// DESCRIPTION :    连接管理服务，负责VPN连接的建立、断开、状态监控和服务器切换，
///                  提供完整的连接生命周期管理和智能重连机制
///
/// AUTHOR :         wei
///
/// HISTORY :        10/06/2025 create

import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/server.dart';
import '../models/interface_info.dart';
import '../core/app_state.dart';
import '../core/error_handler.dart';
import '../core/dependency_injection.dart';
import '../utils/constants.dart';
import '../utils/async_operation_manager.dart';
import '../generated/l10n/app_localizations.dart';
import '../utils/api_exception.dart';
import 'api_service.dart';

import 'language_service.dart';
import 'websocket_service.dart';
import 'log_service.dart';
import 'auth_service.dart';
import '../utils/simple_encryptor.dart';

/// ConnectionManager
///
/// PURPOSE:
///     连接管理服务，负责VPN连接的建立、断开、状态监控和服务器切换
///
/// FEATURES:
///     - 连接管理：建立和断开VPN连接，支持连接状态切换
///     - 服务器管理：服务器选择、切换和延迟测试
///     - 状态监控：实时监控连接状态，智能处理状态变化
///     - 超时处理：连接超时检测和自动清理机制
///     - 错误处理：连接失败处理和用户友好的错误提示
///     - 智能重连：支持服务器切换时的智能重连逻辑
///     - 异步操作：使用非阻塞操作避免UI卡顿
///     - 国际化支持：多语言错误消息和状态提示
///
/// USAGE:
///     通过依赖注入获取实例，监听连接状态变化，调用连接管理方法
class ConnectionManager extends ChangeNotifier {
  Timer? _statusCheckTimer;
  Timer? _reconnectTimer;
  Timer? _connectionTimeoutTimer;
  bool _isConnecting = false;
  bool _isDisconnecting = false;

  late final _ConnectionManagerHelper _helper;

  /// ConnectionManager构造函数
  ///
  /// DESCRIPTION:
  ///     创建连接管理器实例并初始化事件处理器
  ///
  /// PARAMETERS:
  ///     无
  ConnectionManager() {
    _helper = _ConnectionManagerHelper(this);
    // 延迟初始化事件处理器，避免循环依赖
    Future.microtask(() => _initializeEventHandlers());
  }

  /// apiService getter
  ///
  /// DESCRIPTION:
  ///     获取API服务实例
  ///
  /// RETURNS:
  ///     ApiService - API服务实例
  ApiService get apiService => serviceLocator<ApiService>();

  /// webSocketService getter
  ///
  /// DESCRIPTION:
  ///     获取WebSocket服务实例
  ///
  /// RETURNS:
  ///     WebSocketService - WebSocket服务实例
  WebSocketService get webSocketService => serviceLocator<WebSocketService>();

  /// logService getter
  ///
  /// DESCRIPTION:
  ///     获取日志服务实例
  ///
  /// RETURNS:
  ///     LogService - 日志服务实例
  LogService get logService => serviceLocator<LogService>();

  /// appState getter
  ///
  /// DESCRIPTION:
  ///     获取应用状态实例
  ///
  /// RETURNS:
  ///     AppState - 应用状态实例
  AppState get appState => serviceLocator<AppState>();

  /// authService getter
  ///
  /// DESCRIPTION:
  ///     获取认证服务实例
  ///
  /// RETURNS:
  ///     AuthService - 认证服务实例
  AuthService get authService => serviceLocator<AuthService>();

  /// languageService getter
  ///
  /// DESCRIPTION:
  ///     获取语言服务实例
  ///
  /// RETURNS:
  ///     LanguageService - 语言服务实例
  LanguageService get languageService => serviceLocator<LanguageService>();

  /// _getLocalizations
  ///
  /// DESCRIPTION:
  ///     获取本地化文本的辅助方法
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     AppLocalizations? - 本地化实例，可能为null
  AppLocalizations? _getLocalizations() {
    return _helper.getLocalizations();
  }

  /// _getServerDisplayName
  ///
  /// DESCRIPTION:
  ///     获取服务器的本地化显示名称
  ///
  /// PARAMETERS:
  ///     server - 服务器对象
  ///
  /// RETURNS:
  ///     String - 服务器显示名称
  String _getServerDisplayName(Server server) {
    return _helper.getServerDisplayName(server);
  }

  /// _initializeEventHandlers
  ///
  /// DESCRIPTION:
  ///     初始化事件处理器（WebSocket事件处理在DataManager中统一处理）
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     void
  void _initializeEventHandlers() {
    // 注意：WebSocket事件处理在DataManager中统一处理
    // 这里不需要重复注册事件处理器
  }

  /// connect
  ///
  /// DESCRIPTION:
  ///     建立VPN连接到选定的服务器
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<bool> - 连接操作是否成功启动
  ///
  /// THROWS:
  ///     ApiException - API调用失败时抛出异常
  Future<bool> connect() async {
    if (!_canStartConnection()) {
      return false;
    }

    if (!_validateServerSelection()) {
      return false;
    }

    _prepareForConnection();

    try {
      await _executeConnection();
      await _handleConnectionSuccess();
      return true;
    } catch (e) {
      _handleConnectionError(e);
      return false;
    }
  }

  /// _canStartConnection
  ///
  /// DESCRIPTION:
  ///     检查是否可以开始连接
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     bool - 是否可以开始连接
  bool _canStartConnection() {
    // logService.debug('ConnectionManager', 'Connect attempt - _isConnecting: $_isConnecting, _isDisconnecting: $_isDisconnecting, status: ${appState.connectionStatus}');

    if (_isConnecting || appState.connectionStatus == ConnectionStatus.connected) {
      logService.warning('ConnectionManager', 'Connect blocked - already connecting or connected');
      return false;
    }
    return true;
  }

  /// _validateServerSelection
  ///
  /// DESCRIPTION:
  ///     验证服务器选择
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     bool - 服务器选择是否有效
  bool _validateServerSelection() {
    if (appState.selectedServer == null) {
      final l10n = _getLocalizations();
      final message = l10n?.pleaseSelectServer ?? '请先选择服务器';
      ErrorHandler.showWarning(message, context: 'ConnectionManager');
      return false;
    }
    return true;
  }

  /// _prepareForConnection
  ///
  /// DESCRIPTION:
  ///     准备连接，清理状态和设置初始状态
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     void
  void _prepareForConnection() {
    // 清理之前的状态和操作
    _helper.stopAllTimers();
    _helper.cancelAllOperations();
    // logService.debug('ConnectionManager', 'Cleared any pending operations before new connection');

    _isConnecting = true;
    final l10n = _getLocalizations();
    final connectingMessage = l10n?.connectingToServer ?? '正在连接...';
    appState.updateConnectionStatus(ConnectionStatus.connecting, message: connectingMessage);

    // 启动30秒连接超时监控
    _startConnectionTimeoutMonitor();
  }

  /// _executeConnection
  ///
  /// DESCRIPTION:
  ///     执行连接操作
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  ///
  /// THROWS:
  ///     ApiException - API调用失败时抛出异常
  Future<void> _executeConnection() async {
    // final serverName = appState.selectedServer?.name ?? 'Unknown';
    // logService.debug('ConnectionManager', 'Starting API connect call to server: $serverName');

    // 获取用户认证信息
    final username = authService.username;
    final password = authService.password;

    if (username.isEmpty || password.isEmpty) {
      throw ApiException('User credentials not available', 1001, 'credentials_missing');
    }

    // 根据平台决定密码格式，与login方法保持一致
    String connectPassword;
    if (Platform.isIOS || Platform.isMacOS || Platform.isAndroid) {
      // iOS/macOS/Android: 传递明文密码
      connectPassword = password;
      logService.debug('ConnectionManager', 'Using plaintext password for iOS/macOS/Android platform');
    } else {
      // Windows等其他平台: 传递加密密码保持兼容性
      final encryptor = SimpleEncryptor();
      connectPassword = await encryptor.encrypt(password);
      logService.debug('ConnectionManager', 'Using encrypted password for Windows platform');
    }

    final startTime = DateTime.now();
    try {
      final connectResult = await AsyncOperationManager.executeNonBlocking<Map<String, dynamic>?>(
        operation: () async => await apiService.connect(appState.selectedServer!.id, username, connectPassword),
        operationId: 'wan_connect',
        timeout: const Duration(seconds: 25), // 25秒，与iOS和Android后端保持一致
      );
      final duration = DateTime.now().difference(startTime).inMilliseconds;
      logService.info('ConnectionManager', 'API connect call completed successfully in ${duration}ms');

      // Handle interface info from connect response (Windows platform)
      if (connectResult != null && connectResult.containsKey('interface_info')) {
        final interfaceData = connectResult['interface_info'] as Map<String, dynamic>?;
        if (interfaceData != null) {
          final interfaceInfo = InterfaceInfo.fromJson(interfaceData);
          logService.info('ConnectionManager', 'Received interface info from connect response - '
              'interface: ${interfaceInfo.interfaceName}, '
              'local_ip: ${interfaceInfo.localIp}, '
              'tun_ip: ${interfaceInfo.tunIp}');

          // Update app state with interface info
          appState.updateInterfaceInfo(interfaceInfo);
        }
      }

      // Note: Latency info will be received through status events after authentication completes
      // Connect response only contains initial interface info, not authentication latency
    } on PlatformException catch (e) {
      final duration = DateTime.now().difference(startTime).inMilliseconds;
      logService.error('ConnectionManager', 'Platform Channel connection error after ${duration}ms: ${e.message}', e);

      // Handle Platform Channel errors (from Android/iOS backend) and update UI state immediately
      final l10n = _getLocalizations();
      String errorMessage;

      switch (e.code) {
        case 'CONNECTION_FAILED':
        case 'connection_failed':
        case 'TIMEOUT_ERROR':
        case 'timeout_error':
          errorMessage = l10n?.connectionTimeoutDetailed ?? 'Connection timeout, please check network connection or try again later';
          break;
        case 'AUTHENTICATION_FAILED':
        case 'authentication_failed':
          errorMessage = l10n?.authenticationFailed ?? 'Authentication failed, please check credentials';
          break;
        case 'NETWORK_UNAVAILABLE':
        case 'network_unavailable':
          errorMessage = l10n?.connectionFailedGeneric ?? 'Network unavailable, please check network connection';
          break;
        case 'reconnection_failed':
          errorMessage = l10n?.connectionFailedGeneric ?? 'Reconnection failed, please try again';
          break;
        case 'protocol_error':
          errorMessage = l10n?.connectionFailedGeneric ?? 'Protocol error, please check network connection';
          break;
        default:
          errorMessage = e.message ?? (l10n?.connectionFailedGeneric ?? 'Connection failed');
      }

      // Update UI state to error with detailed message immediately
      // This ensures UI doesn't stay in connecting state when backend reports error
      appState.updateConnectionStatus(ConnectionStatus.error, message: errorMessage);

      // Re-throw as ApiException to be handled by caller
      throw ApiException('Platform Channel error: ${e.message}',
          int.tryParse(e.code) ?? 1018, 'platform_error');
    } catch (e) {
      final duration = DateTime.now().difference(startTime).inMilliseconds;
      // logService.debug('ConnectionManager', 'API connect call failed after ${duration}ms: $e');
      logService.error('ConnectionManager', 'API connect call failed after ${duration}ms', e);
      rethrow;
    }
  }

  /// _handleConnectionSuccess
  ///
  /// DESCRIPTION:
  ///     处理连接成功后的逻辑
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  Future<void> _handleConnectionSuccess() async {
    // 先检查一次状态
    await refreshStatus();

    // 只有在仍处于连接状态时才启动状态检查定时器
    if (appState.connectionStatus == ConnectionStatus.connecting) {
      _startStatusCheckTimer();
      // logService.debug('ConnectionManager', 'Connection request sent successfully, starting status monitoring');
    } else {
      // logService.debug('ConnectionManager', 'Connection completed immediately, no need for status monitoring');
    }
  }

  /// _handleConnectionError
  ///
  /// DESCRIPTION:
  ///     处理连接错误
  ///
  /// PARAMETERS:
  ///     error - 错误对象
  ///
  /// RETURNS:
  ///     void
  void _handleConnectionError(dynamic error) {
    _isConnecting = false;
    _stopConnectionTimeoutMonitor();
    _stopStatusCheckTimer();

    logService.error('ConnectionManager', 'Connection error occurred', error);
    // logService.debug('ConnectionManager', 'Error type: ${error.runtimeType}');
    // logService.debug('ConnectionManager', 'Error string: ${error.toString()}');

    // 检查当前状态
    // final currentStatus = appState.connectionStatus;
    // logService.debug('ConnectionManager', 'Current status when handling error: $currentStatus');

    // Check if error state was already set by _executeConnection (for PlatformException)
    // If so, don't override it to preserve detailed error information
    if (appState.connectionStatus == ConnectionStatus.error) {
      logService.info('ConnectionManager', 'Error state already set by _executeConnection, preserving detailed error information');
      ErrorHandler.handleApiError(error, context: 'ConnectionManager');
      return;
    }

    // 检查是否是超时错误
    final l10n = _getLocalizations();
    String errorMessage = l10n?.connectionFailedGeneric ?? 'Connection failed';
    if (error.toString().contains('timeout') || error.toString().contains('超时')) {
      errorMessage = l10n?.connectionTimeoutDetailed ?? 'Connection timeout, please check network connection or try again later';
      logService.error('ConnectionManager', 'Connection API timeout: Unable to send connection request after 10 seconds', error);
    } else {
      logService.error('ConnectionManager', 'Connection API failed', error);
    }

    // Set to error state instead of disconnected to show error to user
    // logService.debug('ConnectionManager', 'Setting status to error with message: $errorMessage');
    appState.updateConnectionStatus(ConnectionStatus.error, message: errorMessage);
    ErrorHandler.handleApiError(error, context: 'ConnectionManager');
  }

  /// disconnect
  ///
  /// DESCRIPTION:
  ///     断开当前的VPN连接
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<bool> - 断开操作是否成功
  ///
  /// THROWS:
  ///     ApiException - API调用失败时抛出异常
  Future<bool> disconnect() async {
    if (_isDisconnecting || appState.connectionStatus == ConnectionStatus.disconnected) {
      return false;
    }

    _prepareForDisconnection();

    try {
      await _executeDisconnection();
      _handleDisconnectionSuccess();
      return true;
    } catch (e) {
      _handleDisconnectionError(e);
      return false;
    } finally {
      _helper.resetConnectionFlags();
    }
  }

  /// _prepareForDisconnection
  ///
  /// DESCRIPTION:
  ///     准备断开连接，设置状态和清理操作
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     void
  void _prepareForDisconnection() {
    _isDisconnecting = true;
    final l10n = _getLocalizations();
    final disconnectingMessage = l10n?.disconnectingFromServer ?? '正在断开连接...';
    appState.updateConnectionStatus(ConnectionStatus.disconnecting, message: disconnectingMessage);

    // 立即清理可能存在的连接操作，避免后续重连被阻止
    AsyncOperationManager.cancelOperation('wan_connect');
    // logService.debug('ConnectionManager', 'Cancelled any pending connect operations before disconnect');
  }

  /// _executeDisconnection
  ///
  /// DESCRIPTION:
  ///     执行断开连接操作
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  ///
  /// THROWS:
  ///     ApiException - API调用失败时抛出异常
  Future<void> _executeDisconnection() async {
    await AsyncOperationManager.executeNonBlocking<void>(
      operation: () => apiService.disconnect(),
      operationId: 'wan_disconnect',
      timeout: const Duration(seconds: 10),
    );
    logService.info('ConnectionManager', 'User requested disconnection');
  }

  /// _handleDisconnectionSuccess
  ///
  /// DESCRIPTION:
  ///     处理断开连接成功后的逻辑
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     void
  void _handleDisconnectionSuccess() {
    // 停止所有定时器
    _helper.stopAllTimers();

    // 立即清除连接时间，确保UI及时更新
    final l10n = _getLocalizations();
    final disconnectedMessage = l10n?.disconnectedFromServer ?? 'Disconnected';
    appState.updateConnectionStatus(
      ConnectionStatus.disconnected,
      message: disconnectedMessage,
      connectedTime: null,
    );

    // logService.debug('ConnectionManager', 'All connection flags reset after successful disconnect');
  }

  /// _handleDisconnectionError
  ///
  /// DESCRIPTION:
  ///     处理断开连接错误
  ///
  /// PARAMETERS:
  ///     error - 错误对象
  ///
  /// RETURNS:
  ///     void
  void _handleDisconnectionError(dynamic error) {
    _stopConnectionTimeoutMonitor();
    logService.warning('ConnectionManager', 'Disconnect failed, but resetting all flags to allow retry');
    ErrorHandler.handleApiError(error, context: 'ConnectionManager');
  }

  /// toggleConnection
  ///
  /// DESCRIPTION:
  ///     根据当前连接状态切换连接（连接或断开）
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<bool> - 切换操作是否成功
  Future<bool> toggleConnection() async {
    switch (appState.connectionStatus) {
      case ConnectionStatus.disconnected:
        return await connect();
      case ConnectionStatus.connected:
        return await disconnect();
      default:
        return false;
    }
  }

  /// selectServer
  ///
  /// DESCRIPTION:
  ///     选择服务器并智能处理连接切换逻辑
  ///
  /// PARAMETERS:
  ///     server - 要选择的服务器对象
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  ///
  /// THROWS:
  ///     ApiException - 连接操作失败时抛出异常
  Future<void> selectServer(Server server) async {
    final currentServer = appState.selectedServer;
    final connectionStatus = appState.connectionStatus;

    // 如果选择的是同一个服务器，直接返回
    if (currentServer?.id == server.id) {
      final serverLogName = _helper.getServerLogName(server);
      logService.info('ConnectionManager', 'Same server already selected: $serverLogName, no need to switch');
      return;
    }

    final newServerLogName = _helper.getServerLogName(server);
    logService.info('ConnectionManager', 'User selected server: $newServerLogName, latency: ${server.ping}ms');

    // 根据当前连接状态选择不同的处理策略
    switch (connectionStatus) {
      case ConnectionStatus.connecting:
        await _handleServerSwitchWhileConnecting(server);
        break;
      case ConnectionStatus.connected:
        await _handleServerSwitchWhileConnected(server);
        break;
      default:
        await _handleServerSelectionWhenDisconnected(server);
        break;
    }
  }

  /// _handleServerSwitchWhileConnecting
  ///
  /// DESCRIPTION:
  ///     处理连接中时的服务器切换
  ///
  /// PARAMETERS:
  ///     server - 新选择的服务器
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  Future<void> _handleServerSwitchWhileConnecting(Server server) async {
    final newServerDisplayName = _getServerDisplayName(server);
    final newServerLogName = _helper.getServerLogName(server);

    logService.info('ConnectionManager', 'Currently connecting, disconnecting first');

    // 立即将UI状态设置为连接中（切换到新服务器）
    final l10n = _getLocalizations();
    final switchingMessage = l10n?.switchingToServer(newServerDisplayName) ?? 'Switching to $newServerDisplayName...';
    appState.updateConnectionStatus(ConnectionStatus.connecting, message: switchingMessage);

    await disconnect();
    await Future.delayed(const Duration(milliseconds: 500));

    // 现在安全地选择新服务器
    appState.selectServer(server, source: ServerSelectionSource.userSelection);
    appState.updateConnectionStatus(ConnectionStatus.connecting, message: switchingMessage);

    // 连接到新服务器
    await _attemptServerConnection(newServerLogName);
  }

  /// _handleServerSwitchWhileConnected
  ///
  /// DESCRIPTION:
  ///     处理已连接时的服务器切换
  ///
  /// PARAMETERS:
  ///     server - 新选择的服务器
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  Future<void> _handleServerSwitchWhileConnected(Server server) async {
    final newServerDisplayName = _getServerDisplayName(server);
    final newServerLogName = _helper.getServerLogName(server);

    logService.info('ConnectionManager', 'Currently connected, switching to new server: $newServerLogName');

    // 立即将UI状态设置为连接中（切换到新服务器）
    final l10n = _getLocalizations();
    final switchingMessage = l10n?.switchingToServer(newServerDisplayName) ?? 'Switching to $newServerDisplayName...';
    appState.updateConnectionStatus(ConnectionStatus.connecting, message: switchingMessage);

    try {
      // 先断开当前连接
      await disconnect();
      await Future.delayed(const Duration(milliseconds: 1000));

      // 现在安全地选择新服务器
      appState.selectServer(server, source: ServerSelectionSource.userSelection);
      appState.updateConnectionStatus(
        ConnectionStatus.connecting,
        message: switchingMessage,
        connectedTime: null,
      );

      // 连接到新服务器
      logService.info('ConnectionManager', 'Starting connection to new server: $newServerLogName');
      await _attemptServerConnection(newServerLogName);
    } catch (e) {
      logService.error('ConnectionManager', 'Error occurred during server switching process', e);
      ErrorHandler.handleApiError(e, context: 'ConnectionManager.selectServer');
    }
  }

  /// _handleServerSelectionWhenDisconnected
  ///
  /// DESCRIPTION:
  ///     处理断开连接状态时的服务器选择
  ///
  /// PARAMETERS:
  ///     server - 新选择的服务器
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  Future<void> _handleServerSelectionWhenDisconnected(Server server) async {
    final newServerLogName = _helper.getServerLogName(server);

    // 如果当前未连接、错误状态或其他状态，直接选择服务器并连接
    appState.selectServer(server, source: ServerSelectionSource.userSelection);
    logService.info('ConnectionManager', 'Current status: ${appState.connectionStatus}, starting connection to new server: $newServerLogName');

    // 立即将UI状态设置为连接中
    final l10n = _getLocalizations();
    final connectingMessage = l10n?.connectingToServer ?? 'Connecting...';
    appState.updateConnectionStatus(ConnectionStatus.connecting, message: connectingMessage);

    await _attemptServerConnection(newServerLogName);
  }

  /// _attemptServerConnection
  ///
  /// DESCRIPTION:
  ///     尝试连接到服务器
  ///
  /// PARAMETERS:
  ///     serverLogName - 服务器日志名称
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  Future<void> _attemptServerConnection(String serverLogName) async {
    try {
      final success = await connect();
      if (success) {
        logService.info('ConnectionManager', 'Successfully connected to server: $serverLogName');
      } else {
        logService.error('ConnectionManager', 'Failed to connect to server: $serverLogName', null);
      }
    } catch (e) {
      logService.error('ConnectionManager', 'Error occurred during connection process', e);
      ErrorHandler.handleApiError(e, context: 'ConnectionManager.selectServer');
    }
  }



  /// pingServers
  ///
  /// DESCRIPTION:
  ///     测试所有服务器的网络延迟
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  ///
  /// THROWS:
  ///     ApiException - 延迟测试失败时抛出异常
  Future<void> pingServers() async {
    // logService.debug('ConnectionManager', 'Starting ping servers operation');

    try {
      // 使用非阻塞操作执行ping测试
      // print('🔍 [PING_DEBUG] ConnectionManager.pingServers() - START');
      logService.debug('ConnectionManager', 'Calling apiService.pingServers() through AsyncOperationManager');
      // print('🔍 [PING_DEBUG] About to call AsyncOperationManager.executeNonBlocking');
      await AsyncOperationManager.executeNonBlocking<void>(
        operation: () => apiService.pingServers(),
        operationId: 'ping_servers',
        timeout: const Duration(seconds: 35), // 增加超时时间以适应iOS后端实际执行时间（通常21秒左右）
      );
      // print('🔍 [PING_DEBUG] AsyncOperationManager.executeNonBlocking completed successfully');

      logService.debug('ConnectionManager', 'Server latency test request sent successfully');
      // print('🔍 [PING_DEBUG] ConnectionManager.pingServers() - SUCCESS');
      // Note: 通知显示已移至UI层面处理，避免硬编码文本和重复通知
    } catch (e) {
      // 检查是否为超时异常，如果是则静默处理，避免误报连接失败
      if (e.toString().contains('TimeoutException')) {
        logService.debug('ConnectionManager', 'Ping servers operation timeout - continuing silently as backend may still be processing');
        // print('🔍 [PING_DEBUG] ConnectionManager.pingServers() - TIMEOUT (silent): ${e.toString()}');
        // 超时时静默处理，不显示错误通知，因为后端可能仍在处理中
        return;
      }
      // print('🔍 [PING_DEBUG] ConnectionManager.pingServers() - ERROR: ${e.toString()}');

      logService.error('ConnectionManager', 'Ping servers operation failed', e);
      ErrorHandler.handleApiError(e, context: 'ConnectionManager');
    }
  }

  /// refreshStatus
  ///
  /// DESCRIPTION:
  ///     获取当前连接状态并智能更新应用状态
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  ///
  /// THROWS:
  ///     ApiException - 状态获取失败时抛出异常
  Future<void> refreshStatus() async {
    try {
      final statusData = await apiService.getStatus();
      final status = _parseConnectionStatus(statusData['status'] as String?);
      final message = statusData['message'] as String? ?? '';

      Server? server;
      if (statusData['server'] != null && statusData['server'] is Map<String, dynamic>) {
        server = Server.fromJson(statusData['server'] as Map<String, dynamic>);
      }

      DateTime? connectedTime;
      if (statusData['connected_time'] != null) {
        try {
          final timestamp = statusData['connected_time'] as int;
          connectedTime = DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
        } catch (e) {
          logService.error('ConnectionManager', 'Failed to parse connected_time', e);
        }
      }

      // 智能状态更新：避免覆盖UI的错误状态
      final currentStatus = appState.connectionStatus;

      // logService.debug('ConnectionManager', 'Backend status update - current: $currentStatus, new: $status, message: $message');

      // 如果UI当前是错误状态，允许后端发送新的错误状态、连接状态或断开连接状态
      if (currentStatus == ConnectionStatus.error) {
        if (status == ConnectionStatus.connected ||
            status == ConnectionStatus.disconnected ||
            status == ConnectionStatus.error) {
          // logService.debug('ConnectionManager', 'Updating from error state to: $status');
          appState.updateConnectionStatus(
            status,
            message: message,
            server: server,
            connectedTime: connectedTime,
          );
        } else {
          // logService.debug('ConnectionManager', 'Ignoring backend status $status while UI is in error state');
          return; // 不更新状态
        }
      } else {
        // 正常情况下更新状态
        // logService.debug('ConnectionManager', 'Normal status update from $currentStatus to $status');
        appState.updateConnectionStatus(
          status,
          message: message,
          server: server,
          connectedTime: connectedTime,
        );
      }

      // 如果连接成功，停止超时监控
      if (status == ConnectionStatus.connected) {
        _stopConnectionTimeoutMonitor();
        // logService.debug('ConnectionManager', 'Connection successful, stopping timeout monitoring');
        _isConnecting = false;
      }

      // 如果连接失败或断开，停止定时器
      if (status == ConnectionStatus.error || status == ConnectionStatus.disconnected) {
        _stopConnectionTimeoutMonitor();
        _stopStatusCheckTimer();
        _isConnecting = false;
        _isDisconnecting = false;
      }
        } catch (e) {
      ErrorHandler.handleApiError(e, context: 'ConnectionManager');
    }
  }

  /// _startConnectionTimeoutMonitor
  ///
  /// DESCRIPTION:
  ///     启动25秒连接超时监控定时器
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     void
  void _startConnectionTimeoutMonitor() {
    _stopConnectionTimeoutMonitor();

    _connectionTimeoutTimer = Timer(const Duration(seconds: 25), () {
      // 检查是否仍在连接状态
      if (appState.connectionStatus == ConnectionStatus.connecting && _isConnecting) {
        logService.info('ConnectionManager', 'Connection timeout after 25 seconds, transitioning to error state');

        // 发送断开连接命令清理后端状态
        _handleConnectionTimeout();
      }
    });

    // logService.debug('ConnectionManager', 'Started 25-second connection timeout monitor');
  }

  /// _stopConnectionTimeoutMonitor
  ///
  /// DESCRIPTION:
  ///     停止连接超时监控定时器
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     void
  void _stopConnectionTimeoutMonitor() {
    _connectionTimeoutTimer?.cancel();
    _connectionTimeoutTimer = null;
  }

  /// _handleConnectionTimeout
  ///
  /// DESCRIPTION:
  ///     处理连接超时，清理状态并显示错误信息
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  void _handleConnectionTimeout() async {
    logService.info('ConnectionManager', 'Handling connection timeout - cleaning up backend state');

    // 重置内部状态
    _isConnecting = false;
    _stopConnectionTimeoutMonitor();
    _stopStatusCheckTimer();

    // 获取本地化消息
    final l10n = _getLocalizations();
    final timeoutMessage = l10n?.connectionTimeoutDetailed ?? 'Connection timeout, please check network connection or try again later';

    // 更新UI状态为错误状态（保持用户选择的服务器）
    appState.updateConnectionStatus(ConnectionStatus.error, message: timeoutMessage);

    // Note: Backend now handles timeout autonomously, no need for UI to reset backend state

    // 等待足够时间让用户看到错误信息，然后重置为断开连接状态允许重试
    Timer(const Duration(seconds: 5), () {
      if (appState.connectionStatus == ConnectionStatus.error) {
        final disconnectedMessage = l10n?.disconnectedFromServer ?? 'Disconnected';
        appState.updateConnectionStatus(ConnectionStatus.disconnected, message: disconnectedMessage);
        logService.info('ConnectionManager', 'Reset to disconnected state after timeout, ready for retry');
      }
    });
  }



  /// _startStatusCheckTimer
  ///
  /// DESCRIPTION:
  ///     启动状态检查定时器，每2秒检查一次连接状态
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     void
  void _startStatusCheckTimer() {
    _stopStatusCheckTimer();
    _statusCheckTimer = Timer.periodic(const Duration(seconds: 3), (timer) {
      refreshStatus();
    });
  }

  /// _stopStatusCheckTimer
  ///
  /// DESCRIPTION:
  ///     停止状态检查定时器
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     void
  void _stopStatusCheckTimer() {
    _statusCheckTimer?.cancel();
    _statusCheckTimer = null;
  }

  /// _parseConnectionStatus
  ///
  /// DESCRIPTION:
  ///     解析连接状态字符串为枚举值
  ///
  /// PARAMETERS:
  ///     statusStr - 状态字符串
  ///
  /// RETURNS:
  ///     ConnectionStatus - 连接状态枚举值
  ConnectionStatus _parseConnectionStatus(String? statusStr) {
    switch (statusStr) {
      case 'connected':
        return ConnectionStatus.connected;
      case 'connecting':
        return ConnectionStatus.connecting;
      case 'disconnecting':
        return ConnectionStatus.disconnecting;
      case 'error':
        return ConnectionStatus.error;
      case 'disconnected':
      default:
        return ConnectionStatus.disconnected;
    }
  }

  /// forceCleanupConnectionState
  ///
  /// DESCRIPTION:
  ///     强制清理连接状态，用于解决状态锁定问题
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     void
  void forceCleanupConnectionState() {
    logService.warning('ConnectionManager', 'Force cleaning connection state');

    // 使用辅助类统一清理状态
    _helper.resetConnectionFlags();
    _helper.cancelAllOperations();
    _helper.stopAllTimers();

    // Ensure UI state is updated to disconnected if it's stuck in connecting state
    if (appState.connectionStatus == ConnectionStatus.connecting) {
      final l10n = _getLocalizations();
      final errorMessage = l10n?.connectionFailedGeneric ?? 'Connection failed, please try again';
      appState.updateConnectionStatus(ConnectionStatus.error, message: errorMessage);
      logService.info('ConnectionManager', 'Updated UI state from connecting to error during cleanup');
    }

    logService.info('ConnectionManager', 'Connection state cleanup completed');
  }

  /// checkAndFixStateInconsistency
  ///
  /// DESCRIPTION:
  ///     检查并修复状态不一致问题
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     void
  void checkAndFixStateInconsistency() {
    final currentStatus = appState.connectionStatus;

    // 如果UI显示为连接中或断开连接中，但内部标志显示操作已完成
    if ((currentStatus == ConnectionStatus.connecting && !_isConnecting) ||
        (currentStatus == ConnectionStatus.disconnecting && !_isDisconnecting)) {
      logService.warning('ConnectionManager', 'Detected state inconsistency, fixing...');

      // 对于错误状态，不要自动重置，保持错误状态让用户看到
      if (currentStatus == ConnectionStatus.error) {
        logService.info('ConnectionManager', 'State inconsistency in error state, keeping error state for user visibility');
        return;
      }

      // 只有在非错误状态下才刷新状态
      refreshStatus();
    }
  }

  /// onConnectionSuccess
  ///
  /// DESCRIPTION:
  ///     连接成功回调，由WebSocket事件处理器调用
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     void
  void onConnectionSuccess() {
    logService.info('ConnectionManager', 'Connection success callback triggered - cleaning up timers');
    _helper.stopAllTimers();
    _isConnecting = false;
  }

  /// onConnectionFailed
  ///
  /// DESCRIPTION:
  ///     连接失败回调，由WebSocket事件处理器调用
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     void
  void onConnectionFailed() {
    // logService.debug('ConnectionManager', 'onConnectionFailed callback triggered');

    // 获取调用栈信息
    // final stackTrace = StackTrace.current;
    // logService.debug('ConnectionManager', 'onConnectionFailed call stack: $stackTrace');

    // 检查当前状态
    // final currentStatus = appState.connectionStatus;
    // logService.debug('ConnectionManager', 'Current status when onConnectionFailed called: $currentStatus');

    // 立即重置内部状态
    _isConnecting = false;
    _helper.stopAllTimers();

    // 使用统一的连接失败处理逻辑
    final l10n = _getLocalizations();
    final errorMessage = l10n?.connectionFailedGeneric ?? 'Connection failed';

    // logService.debug('ConnectionManager', 'Setting status to error via onConnectionFailed');
    // 立即更新状态为错误
    appState.updateConnectionStatus(ConnectionStatus.error, message: errorMessage);

    // Note: Backend now handles connection failures autonomously
    // Note: 不再自动重置错误状态，让用户手动重试
  }



  /// onDisconnected
  ///
  /// DESCRIPTION:
  ///     断开连接回调，由WebSocket事件处理器调用
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     void
  void onDisconnected() {
    // logService.debug('ConnectionManager', 'Disconnected callback triggered');
    _helper.stopAllTimers();
    _helper.resetConnectionFlags();
  }

  /// dispose
  ///
  /// DESCRIPTION:
  ///     清理连接管理器资源，停止所有定时器
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     void
  @override
  void dispose() {
    _helper.stopAllTimers();
    _reconnectTimer?.cancel();
    super.dispose();
  }
}

/// _ConnectionManagerHelper
///
/// PURPOSE:
///     连接管理器辅助类，提供通用的辅助方法和状态管理
///
/// FEATURES:
///     - 本地化消息获取和管理
///     - 状态重置和清理逻辑
///     - 错误处理统一接口
///     - 定时器管理辅助方法
///
/// USAGE:
///     由ConnectionManager内部使用，不对外暴露
class _ConnectionManagerHelper {
  final ConnectionManager _manager;

  /// _ConnectionManagerHelper构造函数
  ///
  /// DESCRIPTION:
  ///     创建辅助类实例
  ///
  /// PARAMETERS:
  ///     manager - ConnectionManager实例
  _ConnectionManagerHelper(this._manager);

  /// getLocalizations
  ///
  /// DESCRIPTION:
  ///     获取本地化文本的辅助方法
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     AppLocalizations? - 本地化实例，可能为null
  AppLocalizations? getLocalizations() {
    try {
      final context = serviceLocator<GlobalKey<NavigatorState>>().currentContext;
      if (context != null) {
        return AppLocalizations.of(context);
      }
    } catch (e) {
      // _manager.logService.debug('ConnectionManager', 'Failed to get localizations: $e');
    }
    return null;
  }

  /// getServerDisplayName
  ///
  /// DESCRIPTION:
  ///     获取服务器的本地化显示名称
  ///
  /// PARAMETERS:
  ///     server - 服务器对象
  ///
  /// RETURNS:
  ///     String - 服务器显示名称
  String getServerDisplayName(Server server) {
    return server.getDisplayNameByLocale(_manager.languageService.isEnglish);
  }

  /// getServerLogName
  ///
  /// DESCRIPTION:
  ///     获取服务器的日志名称（英文优先）
  ///
  /// PARAMETERS:
  ///     server - 服务器对象
  ///
  /// RETURNS:
  ///     String - 服务器日志名称
  String getServerLogName(Server server) {
    return server.nameEn.isNotEmpty ? server.nameEn : server.name;
  }

  /// resetConnectionFlags
  ///
  /// DESCRIPTION:
  ///     重置连接状态标志
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     void
  void resetConnectionFlags() {
    _manager._isConnecting = false;
    _manager._isDisconnecting = false;
  }

  /// stopAllTimers
  ///
  /// DESCRIPTION:
  ///     停止所有定时器
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     void
  void stopAllTimers() {
    _manager._stopStatusCheckTimer();
    _manager._stopConnectionTimeoutMonitor();
  }

  /// cancelAllOperations
  ///
  /// DESCRIPTION:
  ///     取消所有异步操作
  ///
  /// PARAMETERS:
  ///     无
  ///
  /// RETURNS:
  ///     void
  void cancelAllOperations() {
    AsyncOperationManager.cancelOperation('wan_connect');
    AsyncOperationManager.cancelOperation('wan_disconnect');
    AsyncOperationManager.cancelOperation('timeout_cleanup_disconnect');
  }

  /// handleApiError
  ///
  /// DESCRIPTION:
  ///     统一处理API错误
  ///
  /// PARAMETERS:
  ///     error - 错误对象
  ///     context - 错误上下文
  ///
  /// RETURNS:
  ///     void
  void handleApiError(dynamic error, String context) {
    resetConnectionFlags();
    stopAllTimers();
    ErrorHandler.handleApiError(error, context: context);
  }
}
